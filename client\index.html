<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=no, viewport-fit=cover" />
    <title>Free Typing Test - Improve Your WPM Speed | TypingTest</title>
    <meta name="description" content="Take a free typing test to measure your WPM speed and accuracy. Practice typing in multiple languages with real-time feedback and detailed statistics." />
    <meta name="keywords" content="typing test, WPM test, typing speed, keyboard skills, typing practice, multi-language typing" />
    <meta name="robots" content="index, follow" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/svg+xml" sizes="16x16" href="/favicon-16x16.svg" />
    <link rel="icon" type="image/svg+xml" sizes="32x32" href="/favicon-32x32.svg" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.svg" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#3B82F6" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Free Typing Test - Improve Your WPM Speed" />
    <meta property="og:description" content="Measure your typing speed and accuracy with our free typing test. Support for multiple languages and difficulty levels." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://typingtest.com" />
    <meta property="og:image" content="/apple-touch-icon.svg" />
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "TypingTest Typing Test",
        "description": "Free online typing test to measure WPM speed and accuracy",
        "applicationCategory": "EducationalApplication",
        "operatingSystem": "Any",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        }
    }
    </script>
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RCM4MX2590"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-RCM4MX2590');

      // 开发环境调试信息
      if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        console.log('🔍 Google Analytics Debug Info:');
        console.log('- gtag function:', typeof window.gtag);
        console.log('- dataLayer:', window.dataLayer);
        console.log('- GA Measurement ID: G-RCM4MX2590');

        // 延迟检查脚本是否加载
        setTimeout(() => {
          const gtagScript = document.querySelector('script[src*="googletagmanager.com"]');
          console.log('- gtag script element:', gtagScript);
          console.log('- gtag script loaded:', gtagScript ? 'Found' : 'Not found');
        }, 1000);
      }
    </script>

    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Mobile-specific optimizations -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="TypingTest">
    <meta name="format-detection" content="telephone=no">

    <!-- Prevent zoom on input focus for iOS -->
    <style>
      @media screen and (max-width: 767px) {
        input, select, textarea {
          font-size: 16px !important;
        }
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script>
  </body>
</html>
