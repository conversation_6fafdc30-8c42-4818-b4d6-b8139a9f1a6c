import { useState } from "react";
import { Keyboard } from "lucide-react";
import { Link } from "wouter";
import { languageOptions, difficultyOptions } from "@shared/schema";
import { TestConfiguration } from "@/components/test-configuration";
import { StatisticsPanel } from "@/components/statistics-panel";
import { TypingTest } from "@/components/typing-test";
import { OptimizedResultsPanel, OptimizedVirtualKeyboard } from "@/components/performance-optimizations";
import { useTypingTest } from "@/hooks/use-typing-test";
import { getTranslation } from "@/lib/i18n";
import { useLanguage } from "@/contexts/LanguageContext";

export default function Home() {
  const { language, setLanguage, buildUrl } = useLanguage();
  const [difficulty, setDifficulty] = useState("intermediate");
  const [duration, setDuration] = useState(60);
  const [showKeyboard, setShowKeyboard] = useState(true);

  const {
    testState,
    isTestActive,
    isTestCompleted,
    startTest,
    resetTest,
    handleKeyPress,
    handleCompositionInput,
    progress
  } = useTypingTest(language, difficulty, duration);

  return (
    <div className="bg-slate-50 font-sans min-h-screen smooth-scroll safe-area-padding no-zoom">
      {/* Header */}
      <header className="bg-white border-b border-slate-200 sticky top-0 z-50">
        <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row justify-between items-center py-4 sm:py-0 sm:h-16 gap-4 sm:gap-0">
            <div className="flex items-center">
              <h1 className="text-xl sm:text-2xl font-bold text-slate-800">
                <Keyboard className="inline-block w-5 h-5 sm:w-6 sm:h-6 text-blue-600 mr-2" />
                <span className="hidden sm:inline">{getTranslation(language, 'appName')}</span>
                <span className="sm:hidden">TypingTest</span>
              </h1>
            </div>

            <div className="flex items-center space-x-2 sm:space-x-4 w-full sm:w-auto">
              <div className="relative flex-1 sm:flex-none">
                <select
                  value={language}
                  onChange={(e) => setLanguage(e.target.value as any)}
                  disabled={isTestActive}
                  className="appearance-none bg-white border border-slate-300 rounded-lg px-3 sm:px-4 py-2 pr-8 text-sm font-medium text-slate-700 hover:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 w-full sm:w-auto min-h-[44px]"
                >
                  {languageOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="relative flex-1 sm:flex-none">
                <select
                  value={difficulty}
                  onChange={(e) => setDifficulty(e.target.value)}
                  disabled={isTestActive}
                  className="appearance-none bg-white border border-slate-300 rounded-lg px-3 sm:px-4 py-2 pr-8 text-sm font-medium text-slate-700 hover:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 w-full sm:w-auto min-h-[44px]"
                >
                  {difficultyOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {getTranslation(language, option.labelKey)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </nav>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8">
        {/* Test Configuration */}
        <TestConfiguration
          duration={duration}
          setDuration={setDuration}
          isTestActive={isTestActive}
          onStart={startTest}
          onReset={resetTest}
          language={language}
        />

        {/* Statistics Panel */}
        <StatisticsPanel
          wpm={testState.wpm}
          accuracy={testState.accuracy}
          timeRemaining={testState.timeRemaining}
          errors={testState.errors}
          language={language}
        />

        {/* Typing Test Area */}
        <TypingTest
          language={language}
          difficulty={difficulty}
          testState={testState}
          isTestActive={isTestActive}
          onKeyPress={handleKeyPress}
          onCompositionInput={handleCompositionInput}
          progress={progress}
        />

        {/* Results Panel */}
        {isTestCompleted && (
          <OptimizedResultsPanel
            testState={testState}
            language={language}
            difficulty={difficulty}
            duration={duration}
            onRetake={resetTest}
          />
        )}

        {/* Virtual Keyboard */}
        <OptimizedVirtualKeyboard
          isVisible={showKeyboard}
          onToggleVisibility={() => setShowKeyboard(!showKeyboard)}
          currentChar={testState.testText?.[testState.currentPosition]}
          language={language}
          onKeyPress={handleKeyPress}
        />
      </main>

      <footer className="bg-white border-t border-slate-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-slate-600">
            <p>&copy; 2024 {getTranslation(language, 'appName')}. {getTranslation(language, 'footerText')}</p>
            <div className="mt-4 space-x-6">
              <Link href={buildUrl('/privacy-policy')} className="text-sm hover:text-blue-600">
                {getTranslation(language, 'privacyPolicy')}
              </Link>
              <Link href={buildUrl('/terms-of-service')} className="text-sm hover:text-blue-600">
                {getTranslation(language, 'termsOfService')}
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
